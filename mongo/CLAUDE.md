# CLAUDE.md

你是一個資深的軟體工程師。

## 工具和資源

- **文檔查詢**: 需要查閱技術文檔時可以使用 context7 MCP 工具
  - 支援查詢 FastAPI、MongoDB、Redis、SvelteKit 等技術文檔
  - 用法：先解析庫名稱，再獲取相關文檔內容

## 核心開發原則

在整個開發過程中保持高水準的程式碼品質。

### 整潔優先方法

1. 將所有變更分為兩種截然不同的類型：
   - **結構性變更**：在不改變行為的情況下重新安排程式碼（例如：重新命名、提取方法、移動程式碼）
   - **行為性變更**：新增或修改實際功能

2. 切勿在同一個提交中混雜結構性變更與行為性變更

3. 當兩者都需要時，始終先進行結構性變更

4. 透過在變更前後執行測試，驗證結構性變更沒有改變行為

### 提交紀律

僅在以下情況下進行提交：
- 所有測試都已通過
- 所有編譯器/靜態分析器警告都已解決
- 該變更代表單一的邏輯工作單元
- 提交訊息清楚說明該提交包含結構性變更還是行為性變更
- 使用小而頻繁的提交，而非大而稀疏的提交

### 程式碼品質標準

- 無情地消除重複
- 透過命名和結構清晰地表達意圖
- 明確宣告依賴關係
- 保持方法小巧並專注於單一職責
- 最小化狀態和副作用
- 使用最簡單可行的解決方案

## 專案概述

這是一個使用 FastAPI + MongoDB + WebSocket 技術堆疊構建的多人即時聊天室應用程式。目標是打造一個高效、穩定、可擴展的現代化即時通訊平台。

### 核心技術架構

- **後端框架**: FastAPI (支援 WebSocket)
- **資料庫**: MongoDB (用於儲存聊天記錄和房間資料)
- **快取層**: Redis (訊息佇列、連線狀態、快取)
- **即時通訊**: WebSocket (實現低延遲的雙向通訊)
- **認證系統**: JWT Token 認證
- **前端框架**: SvelteKit (現代化響應式前端)

### 核心業務需求

#### 功能性需求
- **即時通訊**: 訊息延遲低於 500ms，支援文字訊息傳送
- **圖片分享**: 支援圖片上傳和顯示
- **多房間支援**: 每個聊天室完全隔離，有獨立的對話脈絡
- **歷史記錄**: 新用戶加入時自動載入最近 50 則歷史訊息
- **使用者管理**: JWT 認證系統，顯示在線使用者列表
- **系統通知**: 使用者加入/離開時自動發送系統訊息

#### 非功能性需求
- **效能**: 95% 訊息延遲 < 500ms，歷史訊息載入 < 2秒
- **容量**: 每個聊天室支援 100 名使用者，系統總計 1000 名使用者
- **可靠性**: 核心服務可用性 99.5%
- **安全性**: JWT 認證、防止 XSS 攻擊、輸入驗證、Rate Limiting

## 專案結構

```
├── backend/                     # FastAPI 後端服務
│   ├── app/                     # 應用程式核心
│   ├── requirements.txt         # Python 依賴
│   ├── Dockerfile               # 後端容器配置
│   └── tests/                   # 單元測試
├── frontend/                    # SvelteKit 前端
│   ├── src/                     # 前端源碼
│   ├── Dockerfile               # 前端容器配置
│   └── package.json             # Node.js 依賴
├── docs/                        # 文檔目錄
│   ├── backend-guide.md         # 後端開發指南
│   ├── frontend-guide.md        # 前端開發指南
│   └── deployment-guide.md      # 部署指南
├── docker-compose.yml           # 基礎服務 (MongoDB + Redis)
├── docker-compose.dev.yml       # 開發環境擴展
├── docker-compose.prod.yml      # 生產環境配置
└── .env.example                 # 環境變數範例
```

## 開發指南導航

- 📖 **後端開發**: 請參閱 [backend-guide.md](docs/backend-guide.md)
- 🎨 **前端開發**: 請參閱 [frontend-guide.md](docs/frontend-guide.md)  
- 🚀 **部署指南**: 請參閱 [deployment-guide.md](docs/deployment-guide.md)

## 開發流程建議

### 第一階段：本機開發（推薦開始方式）
```bash
# 1. 啟動基礎服務
docker-compose up -d

# 2. 本機運行後端 (詳見 backend-guide.md)
cd backend && uvicorn app.main:app --reload

# 3. 本機運行前端 (詳見 frontend-guide.md)  
cd frontend && npm run dev
```

### 第二階段：Docker 開發環境
```bash
# 完整 Docker 開發環境
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

### 第三階段：生產部署
```bash
# 生產環境部署
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 開發時程建議

### 第 1-2 週：基礎架構（採用 TDD）
- [x] 專案結構建立
- [ ] 測試環境配置（pytest + testcontainers）
- [ ] 基礎 FastAPI 應用（API 端點 TDD）
- [ ] MongoDB 連線設定（Repository Pattern）
- [ ] JWT 認證系統（嚴格 TDD）
- [ ] 基礎前端框架

### 第 3-4 週：核心功能（混合測試策略）
- [ ] 聊天室管理（API TDD）
- [ ] WebSocket 連線管理（測試補齊）
- [ ] 即時訊息收發（整合測試）
- [ ] 基礎 UI 界面
- [ ] 用戶認證整合（端到端測試）

### 第 5-6 週：進階功能（重構與優化）
- [ ] 圖片上傳功能（API TDD + 整合測試）
- [ ] 歷史訊息載入（效能測試）
- [ ] 在線用戶顯示
- [ ] 響應式設計
- [ ] 錯誤處理（邊界條件測試）

### 第 7-8 週：優化和部署（品質保證）
- [ ] 效能優化（負載測試）
- [ ] 安全加固（安全測試）
- [ ] 測試覆蓋率達到 80%+
- [ ] 部署準備（自動化測試）
- [ ] 監控設置

## 關鍵架構決策

- **認證優先**: 所有功能都基於 JWT 認證
- **TDD 導向**: 核心業務邏輯採用嚴格 TDD，複雜交互採用測試補齊
- **簡化優先**: 避免過度工程，使用最簡單可行的解決方案
- **Docker 漸進**: 從本機開發逐步過渡到完全容器化
- **文檔分離**: 後端、前端、部署指南獨立維護
- **實務導向**: 提供可實際運行的程式碼範例

## 注意事項

- 優先參考各自的開發指南文檔
- 遵循整潔優先的開發方法
- 保持小而頻繁的提交
- **採用 TDD 時遵循 Red-Green-Refactor 循環**
- **測試覆蓋率維持在 80% 以上**
- 確保所有測試通過才能提交
- 定期檢視和重構程式碼品質