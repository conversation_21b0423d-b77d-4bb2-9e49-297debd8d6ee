# FastAPI 後端開發指南

這是聊天室應用後端的詳細開發指南，使用 FastAPI + MongoDB + Redis + WebSocket 技術棧。

## 目錄

- [環境設定](#環境設定)
- [專案結構](#專案結構)
- [資料模型設計](#資料模型設計)
- [認證系統](#認證系統)
- [API 端點設計](#api-端點設計)
- [WebSocket 實作](#websocket-實作)
- [圖片上傳](#圖片上傳)
- [測試](#測試)
- [常見問題](#常見問題)

## 環境設定

### 基礎依賴

```bash
# 建立虛擬環境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安裝依賴
pip install fastapi uvicorn motor pymongo redis python-jose[cryptography] passlib[bcrypt] python-multipart python-magic
```

### requirements.txt

```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
motor==3.3.2
pymongo==4.6.0
redis==5.0.1
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
python-magic==0.4.27
websockets==12.0
slowapi==0.1.9
```

### 環境變數

```bash
# .env
MONGODB_URL=mongodb://localhost:27017/chatroom
REDIS_URL=redis://localhost:6379/0
JWT_SECRET=your_super_secret_jwt_key_change_in_production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
DEBUG=True
```

## 專案結構

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py                  # FastAPI 應用入口
│   ├── config.py                # 配置管理
│   ├── auth/
│   │   ├── __init__.py
│   │   ├── jwt_handler.py       # JWT 處理
│   │   ├── password.py          # 密碼雜湊
│   │   └── dependencies.py      # 認證依賴
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py              # 使用者模型
│   │   ├── message.py           # 訊息模型
│   │   └── room.py              # 房間模型
│   ├── database/
│   │   ├── __init__.py
│   │   ├── mongodb.py           # MongoDB 連線
│   │   └── redis_client.py      # Redis 連線
│   ├── routers/
│   │   ├── __init__.py
│   │   ├── auth.py              # 認證路由
│   │   ├── rooms.py             # 房間管理
│   │   ├── messages.py          # 訊息處理
│   │   └── upload.py            # 檔案上傳
│   ├── websocket/
│   │   ├── __init__.py
│   │   ├── manager.py           # 連線管理
│   │   └── events.py            # 事件處理
│   └── utils/
│       ├── __init__.py
│       └── validators.py        # 輸入驗證
├── tests/                       # 測試檔案
├── requirements.txt
└── Dockerfile
```

## 資料模型設計

### 使用者模型 (models/user.py)

```python
from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime
from bson import ObjectId

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid objectid")
        return ObjectId(v)

    @classmethod
    def __modify_schema__(cls, field_schema):
        field_schema.update(type="string")

class UserModel(BaseModel):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    username: str = Field(..., min_length=3, max_length=20)
    email: EmailStr
    password_hash: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class UserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=20)
    email: EmailStr
    password: str = Field(..., min_length=6)

class UserResponse(BaseModel):
    id: str
    username: str
    email: EmailStr
    created_at: datetime
    is_active: bool
```

### 訊息模型 (models/message.py)

```python
from pydantic import BaseModel, Field
from typing import Optional, Literal
from datetime import datetime
from bson import ObjectId

class MessageModel(BaseModel):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    room_id: str
    user_id: str
    username: str
    message_type: Literal["text", "image", "system", "user_join", "user_leave"] = "text"
    content: Optional[str] = None
    image_id: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    edited_at: Optional[datetime] = None
    reply_to: Optional[str] = None

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class MessageCreate(BaseModel):
    message_type: Literal["text", "image"] = "text"
    content: Optional[str] = None
    image_id: Optional[str] = None
    reply_to: Optional[str] = None

class MessageResponse(BaseModel):
    id: str
    room_id: str
    user_id: str
    username: str
    message_type: str
    content: Optional[str]
    image_id: Optional[str]
    created_at: datetime
    edited_at: Optional[datetime]
    reply_to: Optional[str]
```

### 房間模型 (models/room.py)

```python
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from bson import ObjectId

class RoomModel(BaseModel):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=200)
    owner_id: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_private: bool = False
    max_members: int = 100

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class RoomCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=200)
    is_private: bool = False
    max_members: int = 100

class RoomResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    owner_id: str
    created_at: datetime
    is_private: bool
    max_members: int
    member_count: int
```

## 認證系統

### JWT 處理 (auth/jwt_handler.py)

```python
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from app.config import settings

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.JWT_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    try:
        payload = jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            return None
        return {"user_id": user_id}
    except JWTError:
        return None
```

### 密碼處理 (auth/password.py)

```python
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)
```

### 認證依賴 (auth/dependencies.py)

```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from app.auth.jwt_handler import verify_token
from app.database.mongodb import get_database
from app.models.user import UserModel

security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> UserModel:
    token = credentials.credentials
    token_data = verify_token(token)
    
    if token_data is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    db = await get_database()
    user = await db.users.find_one({"_id": ObjectId(token_data["user_id"])})
    
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return UserModel(**user)
```

## API 端點設計

### 認證路由 (routers/auth.py)

```python
from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.security import OAuth2PasswordRequestForm
from datetime import timedelta
from app.models.user import UserCreate, UserResponse, UserModel
from app.auth.password import hash_password, verify_password
from app.auth.jwt_handler import create_access_token
from app.database.mongodb import get_database
from app.config import settings

router = APIRouter(prefix="/auth", tags=["authentication"])

@router.post("/register", response_model=dict)
async def register_user(user: UserCreate):
    db = await get_database()
    
    # 檢查用戶是否已存在
    existing_user = await db.users.find_one({
        "$or": [
            {"email": user.email},
            {"username": user.username}
        ]
    })
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email or username already registered"
        )
    
    # 創建新用戶
    user_data = UserModel(
        username=user.username,
        email=user.email,
        password_hash=hash_password(user.password)
    )
    
    result = await db.users.insert_one(user_data.dict(by_alias=True))
    
    # 生成 JWT token
    access_token_expires = timedelta(minutes=settings.JWT_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(result.inserted_id)},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": UserResponse(
            id=str(result.inserted_id),
            username=user.username,
            email=user.email,
            created_at=user_data.created_at,
            is_active=True
        )
    }

@router.post("/login")
async def login_user(form_data: OAuth2PasswordRequestForm = Depends()):
    db = await get_database()
    
    # 查找用戶
    user = await db.users.find_one({"username": form_data.username})
    
    if not user or not verify_password(form_data.password, user["password_hash"]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 生成 JWT token
    access_token_expires = timedelta(minutes=settings.JWT_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user["_id"])},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": UserResponse(
            id=str(user["_id"]),
            username=user["username"],
            email=user["email"],
            created_at=user["created_at"],
            is_active=user["is_active"]
        )
    }

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: UserModel = Depends(get_current_user)):
    return UserResponse(
        id=str(current_user.id),
        username=current_user.username,
        email=current_user.email,
        created_at=current_user.created_at,
        is_active=current_user.is_active
    )
```

## WebSocket 實作

### 連線管理 (websocket/manager.py)

```python
from typing import List, Dict
from fastapi import WebSocket, WebSocketDisconnect
from app.models.message import MessageModel
from app.database.mongodb import get_database
from app.database.redis_client import get_redis_client
import json
import asyncio

class ConnectionManager:
    def __init__(self):
        # room_id -> List[WebSocket]
        self.active_connections: Dict[str, List[WebSocket]] = {}
        # websocket -> user_info
        self.user_connections: Dict[WebSocket, dict] = {}

    async def connect(self, websocket: WebSocket, room_id: str, user_info: dict):
        await websocket.accept()
        
        if room_id not in self.active_connections:
            self.active_connections[room_id] = []
        
        self.active_connections[room_id].append(websocket)
        self.user_connections[websocket] = {
            "room_id": room_id,
            "user_id": user_info["user_id"],
            "username": user_info["username"]
        }
        
        # 發送用戶加入通知
        await self.broadcast_to_room(room_id, {
            "message_type": "user_join",
            "user_id": user_info["user_id"],
            "username": user_info["username"],
            "content": f"{user_info['username']} 加入了聊天室",
            "created_at": datetime.utcnow().isoformat()
        })

    def disconnect(self, websocket: WebSocket):
        user_info = self.user_connections.get(websocket)
        if user_info:
            room_id = user_info["room_id"]
            if room_id in self.active_connections:
                self.active_connections[room_id].remove(websocket)
                if not self.active_connections[room_id]:
                    del self.active_connections[room_id]
            
            del self.user_connections[websocket]
            
            # 發送用戶離開通知 (需要在其他地方處理)
            return user_info
        
        return None

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast_to_room(self, room_id: str, message: dict):
        if room_id in self.active_connections:
            message_str = json.dumps(message, default=str)
            disconnected_connections = []
            
            for connection in self.active_connections[room_id]:
                try:
                    await connection.send_text(message_str)
                except:
                    disconnected_connections.append(connection)
            
            # 清理斷開的連線
            for conn in disconnected_connections:
                self.disconnect(conn)

    def get_room_users(self, room_id: str) -> List[dict]:
        if room_id not in self.active_connections:
            return []
        
        users = []
        for websocket in self.active_connections[room_id]:
            user_info = self.user_connections.get(websocket)
            if user_info:
                users.append({
                    "user_id": user_info["user_id"],
                    "username": user_info["username"]
                })
        
        return users

manager = ConnectionManager()
```

### WebSocket 端點 (main.py 中)

```python
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends
from app.websocket.manager import manager
from app.auth.dependencies import get_current_user_from_token
from app.models.message import MessageModel, MessageCreate
from datetime import datetime

@app.websocket("/ws/{room_id}")
async def websocket_endpoint(websocket: WebSocket, room_id: str):
    # 從查詢參數或 header 獲取 token
    token = None
    
    # 嘗試從查詢參數獲取 token
    query_params = dict(websocket.query_params)
    if "token" in query_params:
        token = query_params["token"]
    
    if not token:
        await websocket.close(code=1008, reason="Missing authentication token")
        return
    
    # 驗證 token
    try:
        user_info = await verify_websocket_token(token)
        if not user_info:
            await websocket.close(code=1008, reason="Invalid token")
            return
    except Exception:
        await websocket.close(code=1008, reason="Authentication failed")
        return
    
    # 建立連線
    await manager.connect(websocket, room_id, user_info)
    
    try:
        while True:
            # 接收訊息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 處理不同類型的訊息
            if message_data.get("message_type") == "text":
                # 儲存訊息到資料庫
                db = await get_database()
                message = MessageModel(
                    room_id=room_id,
                    user_id=user_info["user_id"],
                    username=user_info["username"],
                    message_type="text",
                    content=message_data.get("content"),
                    created_at=datetime.utcnow()
                )
                
                result = await db.messages.insert_one(message.dict(by_alias=True))
                message.id = result.inserted_id
                
                # 廣播給房間內所有用戶
                await manager.broadcast_to_room(room_id, {
                    "message_id": str(message.id),
                    "room_id": room_id,
                    "user_id": user_info["user_id"],
                    "username": user_info["username"],
                    "message_type": "text",
                    "content": message_data.get("content"),
                    "created_at": message.created_at.isoformat()
                })
            
            elif message_data.get("message_type") == "image":
                # 處理圖片訊息
                image_id = message_data.get("image_id")
                if image_id:
                    db = await get_database()
                    message = MessageModel(
                        room_id=room_id,
                        user_id=user_info["user_id"],
                        username=user_info["username"],
                        message_type="image",
                        image_id=image_id,
                        created_at=datetime.utcnow()
                    )
                    
                    result = await db.messages.insert_one(message.dict(by_alias=True))
                    
                    await manager.broadcast_to_room(room_id, {
                        "message_id": str(result.inserted_id),
                        "room_id": room_id,
                        "user_id": user_info["user_id"],
                        "username": user_info["username"],
                        "message_type": "image",
                        "image_id": image_id,
                        "created_at": message.created_at.isoformat()
                    })
                
    except WebSocketDisconnect:
        user_info = manager.disconnect(websocket)
        if user_info:
            # 發送用戶離開通知
            await manager.broadcast_to_room(user_info["room_id"], {
                "message_type": "user_leave",
                "user_id": user_info["user_id"],
                "username": user_info["username"],
                "content": f"{user_info['username']} 離開了聊天室",
                "created_at": datetime.utcnow().isoformat()
            })
```

## 圖片上傳

### 上傳路由 (routers/upload.py)

```python
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from motor.motor_asyncio import AsyncIOMotorGridFSBucket
from app.database.mongodb import get_database
from app.auth.dependencies import get_current_user
from app.models.user import UserModel
import magic
from bson import ObjectId

router = APIRouter(prefix="/upload", tags=["upload"])

ALLOWED_TYPES = [
    "image/jpeg",
    "image/png", 
    "image/gif",
    "image/webp"
]

MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

@router.post("/image")
async def upload_image(
    image: UploadFile = File(...),
    current_user: UserModel = Depends(get_current_user)
):
    # 驗證檔案類型
    file_content = await image.read()
    file_type = magic.from_buffer(file_content, mime=True)
    
    if file_type not in ALLOWED_TYPES:
        raise HTTPException(
            status_code=400,
            detail="Unsupported file type. Only JPEG, PNG, GIF, WebP are allowed."
        )
    
    # 驗證檔案大小
    if len(file_content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail="File too large. Maximum size is 10MB."
        )
    
    # 儲存到 GridFS
    db = await get_database()
    fs = AsyncIOMotorGridFSBucket(db)
    
    file_id = await fs.upload_from_stream(
        image.filename,
        file_content,
        metadata={
            "content_type": file_type,
            "user_id": str(current_user.id),
            "file_size": len(file_content)
        }
    )
    
    return {
        "file_id": str(file_id),
        "filename": image.filename,
        "content_type": file_type,
        "file_size": len(file_content)
    }

@router.get("/image/{file_id}")
async def get_image(file_id: str):
    try:
        db = await get_database()
        fs = AsyncIOMotorGridFSBucket(db)
        
        # 檢查檔案是否存在
        file_info = await fs.find({"_id": ObjectId(file_id)}).to_list(1)
        if not file_info:
            raise HTTPException(status_code=404, detail="File not found")
        
        # 讀取檔案內容
        file_data = await fs.open_download_stream(ObjectId(file_id)).read()
        
        # 返回檔案內容
        from fastapi.responses import Response
        return Response(
            content=file_data,
            media_type=file_info[0].metadata.get("content_type", "application/octet-stream")
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## TDD 開發策略

### 為什麼在聊天室後端使用 TDD

聊天室後端適合採用**漸進式 TDD**策略，針對不同模組採用不同的測試方法：

| 模組類型 | TDD 適用性 | 策略 |
|---------|-----------|------|
| 認證系統 | ⭐⭐⭐⭐⭐ | 嚴格 TDD |
| REST API | ⭐⭐⭐⭐⭐ | 嚴格 TDD |
| 業務邏輯 | ⭐⭐⭐⭐⭐ | 嚴格 TDD |
| 資料庫操作 | ⭐⭐⭐ | 測試優先 |
| Redis 操作 | ⭐⭐⭐ | 測試優先 |
| WebSocket | ⭐⭐ | 測試補齊 |
| 併發處理 | ⭐⭐ | 測試補齊 |

### TDD 實施原則

#### 1. Red-Green-Refactor 循環

```python
# Red: 寫一個失敗的測試
def test_create_user_success():
    user_data = UserCreate(
        username="testuser",
        email="<EMAIL>", 
        password="testpass123"
    )
    # 這裡會失敗，因為函數還不存在
    result = create_user(user_data)
    assert result.username == "testuser"

# Green: 寫最少的代碼讓測試通過
async def create_user(user_data: UserCreate) -> UserModel:
    return UserModel(
        username=user_data.username,
        email=user_data.email,
        password_hash="dummy"
    )

# Refactor: 重構改善代碼
async def create_user(user_data: UserCreate) -> UserModel:
    password_hash = hash_password(user_data.password)
    return UserModel(
        username=user_data.username,
        email=user_data.email,
        password_hash=password_hash
    )
```

#### 2. 分層測試策略

```python
# 單元測試：純業務邏輯
def test_password_validation():
    assert validate_password("123456") == True
    assert validate_password("12345") == False

# 整合測試：API 端點
async def test_register_endpoint():
    response = await client.post("/auth/register", json={
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpass123"
    })
    assert response.status_code == 200

# 端到端測試：完整流程
async def test_user_registration_flow():
    # 註冊 -> 登入 -> 獲取用戶資訊
    pass
```

### TDD 工具鏈配置

#### pytest 配置 (pyproject.toml)

```toml
[tool.pytest.ini_options]
testpaths = ["tests"]
asyncio_mode = "auto"
addopts = """
    --cov=app
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
    -v
"""
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning"
]
```

#### 測試依賴 (requirements-test.txt)

```txt
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
testcontainers==3.7.1
factory-boy==3.3.0
faker==20.1.0
```

### 階段式 TDD 實施

#### 階段一：認證系統 TDD

**1. JWT Token 處理**

```python
# tests/test_auth/test_jwt.py
import pytest
from datetime import timedelta
from app.auth.jwt_handler import create_access_token, verify_token

class TestJWTHandler:
    def test_create_access_token_success(self):
        """測試成功創建 JWT token"""
        data = {"sub": "user123"}
        token = create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 50  # JWT token 應該有一定長度

    def test_verify_token_success(self):
        """測試成功驗證 JWT token"""
        data = {"sub": "user123"}
        token = create_access_token(data)
        
        payload = verify_token(token)
        assert payload["user_id"] == "user123"

    def test_verify_invalid_token(self):
        """測試驗證無效 token"""
        invalid_token = "invalid.token.here"
        
        result = verify_token(invalid_token)
        assert result is None

    def test_token_expiration(self):
        """測試 token 過期"""
        data = {"sub": "user123"}
        # 創建已過期的 token（負數時間）
        token = create_access_token(data, timedelta(seconds=-1))
        
        result = verify_token(token)
        assert result is None
```

**2. 密碼處理**

```python
# tests/test_auth/test_password.py
import pytest
from app.auth.password import hash_password, verify_password

class TestPasswordHandler:
    def test_hash_password(self):
        """測試密碼雜湊"""
        password = "testpass123"
        hashed = hash_password(password)
        
        assert hashed != password
        assert len(hashed) > 50
        assert hashed.startswith('$2b$')

    def test_verify_password_success(self):
        """測試密碼驗證成功"""
        password = "testpass123"
        hashed = hash_password(password)
        
        assert verify_password(password, hashed) == True

    def test_verify_password_failure(self):
        """測試密碼驗證失敗"""
        password = "testpass123"
        wrong_password = "wrongpass"
        hashed = hash_password(password)
        
        assert verify_password(wrong_password, hashed) == False
```

#### 階段二：API 端點 TDD

**1. 用戶註冊 API**

```python
# tests/test_routers/test_auth.py
import pytest
from httpx import AsyncClient
from app.main import app

class TestAuthRouter:
    @pytest.fixture
    async def client(self):
        async with AsyncClient(app=app, base_url="http://test") as ac:
            yield ac

    async def test_register_success(self, client):
        """測試用戶註冊成功"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        
        response = await client.post("/auth/register", json=user_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["user"]["username"] == "testuser"
        assert data["user"]["email"] == "<EMAIL>"

    async def test_register_duplicate_username(self, client):
        """測試重複用戶名註冊"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        
        # 第一次註冊
        await client.post("/auth/register", json=user_data)
        
        # 第二次註冊相同用戶名
        response = await client.post("/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "already registered" in response.json()["detail"]

    async def test_register_invalid_email(self, client):
        """測試無效郵箱格式"""
        user_data = {
            "username": "testuser",
            "email": "invalid-email",
            "password": "testpass123"
        }
        
        response = await client.post("/auth/register", json=user_data)
        assert response.status_code == 422

    async def test_register_weak_password(self, client):
        """測試弱密碼"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "123"  # 太短
        }
        
        response = await client.post("/auth/register", json=user_data)
        assert response.status_code == 422
```

#### 階段三：Repository Pattern TDD

**1. 用戶資料庫操作**

```python
# tests/test_repositories/test_user_repository.py
import pytest
from app.repositories.user_repository import UserRepository
from app.models.user import UserCreate, UserModel
from tests.factories import UserFactory

class TestUserRepository:
    @pytest.fixture
    async def user_repo(self, test_db):
        return UserRepository(test_db)

    async def test_create_user(self, user_repo):
        """測試創建用戶"""
        user_data = UserFactory.build()
        
        result = await user_repo.create_user(user_data)
        
        assert isinstance(result, UserModel)
        assert result.username == user_data.username
        assert result.email == user_data.email
        assert result.id is not None

    async def test_get_user_by_username(self, user_repo):
        """測試根據用戶名獲取用戶"""
        # 先創建用戶
        user_data = UserFactory.build()
        created_user = await user_repo.create_user(user_data)
        
        # 查詢用戶
        found_user = await user_repo.get_by_username(user_data.username)
        
        assert found_user is not None
        assert found_user.id == created_user.id

    async def test_get_nonexistent_user(self, user_repo):
        """測試獲取不存在的用戶"""
        result = await user_repo.get_by_username("nonexistent")
        assert result is None
```

#### 階段四：WebSocket 測試補齊

```python
# tests/test_websocket/test_connection.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

class TestWebSocketConnection:
    def test_websocket_connection_success(self):
        """測試 WebSocket 連線成功"""
        client = TestClient(app)
        
        with client.websocket_connect("/ws/test-room?token=valid_token") as websocket:
            # 測試連線建立
            data = websocket.receive_json()
            assert data["message_type"] == "connection_established"

    def test_websocket_authentication_failure(self):
        """測試 WebSocket 認證失敗"""
        client = TestClient(app)
        
        with pytest.raises(Exception):  # 連線應該被拒絕
            client.websocket_connect("/ws/test-room?token=invalid_token")

    def test_websocket_message_broadcast(self):
        """測試訊息廣播"""
        client = TestClient(app)
        
        # 創建兩個連線
        with client.websocket_connect("/ws/test-room?token=token1") as ws1, \
             client.websocket_connect("/ws/test-room?token=token2") as ws2:
            
            # 從第一個連線發送訊息
            ws1.send_json({
                "message_type": "text",
                "content": "Hello World"
            })
            
            # 第二個連線應該收到訊息
            message = ws2.receive_json()
            assert message["content"] == "Hello World"
```

### 測試資料工廠

```python
# tests/factories.py
import factory
from factory import Faker
from app.models.user import UserCreate

class UserFactory(factory.Factory):
    class Meta:
        model = UserCreate

    username = Faker('user_name')
    email = Faker('email')
    password = "testpass123"

class MessageFactory(factory.Factory):
    class Meta:
        model = dict

    room_id = "test-room"
    message_type = "text"
    content = Faker('sentence')
```

### TDD 開發流程建議

#### 每日開發循環

```bash
# 1. 運行現有測試確保沒有回歸
pytest

# 2. 寫一個新的失敗測試
# 編輯 tests/test_xxx.py

# 3. 運行測試看到失敗（Red）
pytest tests/test_xxx.py::test_new_feature -v

# 4. 寫最少代碼讓測試通過（Green）
# 編輯 app/xxx.py

# 5. 運行測試確保通過
pytest tests/test_xxx.py::test_new_feature -v

# 6. 重構改善代碼（Refactor）
# 優化實作，確保測試仍然通過

# 7. 運行完整測試套件
pytest --cov=app
```

#### 提交前檢查清單

- [ ] 所有測試都通過
- [ ] 測試覆蓋率 > 80%
- [ ] 沒有測試警告
- [ ] 代碼符合 PEP 8 規範
- [ ] 提交訊息說明變更類型（功能/重構）

## 測試

### 基本測試設置 (tests/conftest.py)

```python
import pytest
import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from fastapi.testclient import TestClient
from app.main import app
from app.config import settings

@pytest.fixture(scope="session")
def event_loop():
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def test_db():
    client = AsyncIOMotorClient(settings.MONGODB_URL)
    db = client.test_chatroom
    yield db
    # 清理測試資料
    await client.drop_database("test_chatroom")
    client.close()

@pytest.fixture
def client():
    return TestClient(app)
```

### 認證測試 (tests/test_auth.py)

```python
import pytest
from fastapi.testclient import TestClient

def test_register_user(client: TestClient):
    response = client.post("/auth/register", json={
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpass123"
    })
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["user"]["username"] == "testuser"

def test_login_user(client: TestClient):
    # 先註冊用戶
    client.post("/auth/register", json={
        "username": "testuser2",
        "email": "<EMAIL>", 
        "password": "testpass123"
    })
    
    # 登入
    response = client.post("/auth/login", data={
        "username": "testuser2",
        "password": "testpass123"
    })
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
```

## 效能優化

### 效能瓶頸分析

聊天室應用的主要效能挑戰：

| 瓶頸點 | 影響 | 優化重要性 |
|--------|------|-----------|
| 資料庫查詢 | 延遲、吞吐量 | ⭐⭐⭐⭐⭐ |
| WebSocket 連線管理 | 記憶體、CPU | ⭐⭐⭐⭐⭐ |
| 訊息廣播 | 網路頻寬 | ⭐⭐⭐⭐ |
| 圖片上傳/下載 | I/O、儲存 | ⭐⭐⭐ |
| 認證驗證 | CPU、延遲 | ⭐⭐⭐ |

### 資料庫索引優化

#### 1. MongoDB 索引設計

```python
# database/indexes.py
from motor.motor_asyncio import AsyncIOMotorDatabase

async def create_performance_indexes(db: AsyncIOMotorDatabase):
    """建立效能關鍵索引"""
    
    # 1. 訊息查詢索引（最重要）
    await db.messages.create_index([
        ("room_id", 1),
        ("created_at", -1)
    ], name="messages_room_time_idx")
    
    # 2. 複合索引：房間 + 訊息類型
    await db.messages.create_index([
        ("room_id", 1),
        ("message_type", 1),
        ("created_at", -1)
    ], name="messages_room_type_time_idx")
    
    # 3. 使用者唯一索引
    await db.users.create_index(
        [("username", 1)], 
        unique=True, 
        name="users_username_unique_idx"
    )
    await db.users.create_index(
        [("email", 1)], 
        unique=True, 
        name="users_email_unique_idx"
    )
    
    # 4. 房間查詢索引
    await db.rooms.create_index([
        ("is_private", 1),
        ("created_at", -1)
    ], name="rooms_privacy_time_idx")
    
    # 5. GridFS 檔案索引
    await db.fs.files.create_index([
        ("metadata.user_id", 1),
        ("uploadDate", -1)
    ], name="files_user_time_idx")

async def create_text_search_indexes(db: AsyncIOMotorDatabase):
    """建立全文搜尋索引"""
    await db.messages.create_index([
        ("content", "text"),
        ("username", "text")
    ], name="messages_text_search_idx")
```

#### 2. 查詢優化模式

```python
# repositories/message_repository.py
from typing import List, Optional
from datetime import datetime, timedelta
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.models.message import MessageModel

class MessageRepository:
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.collection = db.messages

    async def get_recent_messages(
        self, 
        room_id: str, 
        limit: int = 50,
        before_time: Optional[datetime] = None
    ) -> List[MessageModel]:
        """高效能分頁查詢歷史訊息"""
        
        query = {"room_id": room_id}
        if before_time:
            query["created_at"] = {"$lt": before_time}
        
        # 使用索引進行排序和限制
        cursor = self.collection.find(query).sort([
            ("created_at", -1)
        ]).limit(limit)
        
        messages = await cursor.to_list(length=limit)
        return [MessageModel(**msg) for msg in reversed(messages)]

    async def get_messages_by_type(
        self,
        room_id: str,
        message_type: str,
        limit: int = 20
    ) -> List[MessageModel]:
        """根據類型高效查詢訊息"""
        
        cursor = self.collection.find({
            "room_id": room_id,
            "message_type": message_type
        }).sort([("created_at", -1)]).limit(limit)
        
        messages = await cursor.to_list(length=limit)
        return [MessageModel(**msg) for msg in messages]

    async def get_message_count_by_room(self, room_id: str) -> int:
        """使用索引統計房間訊息數量"""
        return await self.collection.count_documents({"room_id": room_id})
```

### Redis 快取層實作

#### 1. 多層快取策略

```python
# database/cache_manager.py
import json
import asyncio
from typing import Optional, List, Any
from datetime import timedelta
from redis.asyncio import Redis
from app.models.message import MessageModel

class CacheManager:
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        
        # 快取過期時間設定
        self.CACHE_TTL = {
            "user_session": timedelta(minutes=30),
            "room_messages": timedelta(minutes=10),
            "room_users": timedelta(minutes=5),
            "user_profile": timedelta(hours=1),
            "room_info": timedelta(hours=2)
        }

    async def cache_recent_messages(
        self, 
        room_id: str, 
        messages: List[MessageModel]
    ):
        """快取最近訊息"""
        cache_key = f"room:{room_id}:recent_messages"
        
        # 序列化訊息
        message_data = [msg.dict() for msg in messages]
        
        await self.redis.setex(
            cache_key,
            self.CACHE_TTL["room_messages"],
            json.dumps(message_data, default=str)
        )

    async def get_cached_messages(
        self, 
        room_id: str
    ) -> Optional[List[MessageModel]]:
        """獲取快取的訊息"""
        cache_key = f"room:{room_id}:recent_messages"
        
        cached_data = await self.redis.get(cache_key)
        if not cached_data:
            return None
        
        try:
            message_data = json.loads(cached_data)
            return [MessageModel(**msg) for msg in message_data]
        except (json.JSONDecodeError, ValueError):
            await self.redis.delete(cache_key)
            return None

    async def cache_room_users(self, room_id: str, users: List[dict]):
        """快取房間在線使用者"""
        cache_key = f"room:{room_id}:online_users"
        
        await self.redis.setex(
            cache_key,
            self.CACHE_TTL["room_users"],
            json.dumps(users)
        )

    async def get_room_user_count(self, room_id: str) -> int:
        """快速獲取房間人數"""
        cache_key = f"room:{room_id}:online_users"
        
        cached_data = await self.redis.get(cache_key)
        if cached_data:
            try:
                users = json.loads(cached_data)
                return len(users)
            except json.JSONDecodeError:
                pass
        
        return 0

    async def invalidate_room_cache(self, room_id: str):
        """清除房間相關快取"""
        keys_to_delete = [
            f"room:{room_id}:recent_messages",
            f"room:{room_id}:online_users",
            f"room:{room_id}:info"
        ]
        
        await self.redis.delete(*keys_to_delete)
```

#### 2. Write-Through 快取模式

```python
# services/message_service.py
from app.database.cache_manager import CacheManager
from app.repositories.message_repository import MessageRepository

class MessageService:
    def __init__(
        self, 
        message_repo: MessageRepository, 
        cache_manager: CacheManager
    ):
        self.message_repo = message_repo
        self.cache = cache_manager

    async def get_room_messages(
        self, 
        room_id: str, 
        limit: int = 50
    ) -> List[MessageModel]:
        """高效能訊息查詢（快取優先）"""
        
        # 1. 嘗試從快取獲取
        cached_messages = await self.cache.get_cached_messages(room_id)
        if cached_messages and len(cached_messages) >= limit:
            return cached_messages[:limit]
        
        # 2. 從資料庫查詢
        messages = await self.message_repo.get_recent_messages(
            room_id, limit
        )
        
        # 3. 更新快取
        if messages:
            await self.cache.cache_recent_messages(room_id, messages)
        
        return messages

    async def create_message(
        self, 
        room_id: str, 
        message_data: dict
    ) -> MessageModel:
        """創建訊息並更新快取"""
        
        # 1. 儲存到資料庫
        message = await self.message_repo.create_message(
            room_id, message_data
        )
        
        # 2. 清除相關快取（讓下次查詢重新載入）
        await self.cache.invalidate_room_cache(room_id)
        
        return message
```

### WebSocket 連線優化

#### 1. Redis 分散式連線管理

```python
# websocket/redis_connection_manager.py
import json
import asyncio
from typing import Dict, List, Set
from redis.asyncio import Redis
from fastapi import WebSocket

class RedisConnectionManager:
    def __init__(self, redis_client: Redis, server_id: str):
        self.redis = redis_client
        self.server_id = server_id
        
        # 本機連線管理
        self.local_connections: Dict[str, Set[WebSocket]] = {}
        self.websocket_to_user: Dict[WebSocket, dict] = {}
        
        # Redis 訂閱
        self.pubsub = None

    async def start_message_subscriber(self):
        """啟動 Redis 訊息訂閱"""
        self.pubsub = self.redis.pubsub()
        await self.pubsub.subscribe("chatroom:broadcast")
        
        # 背景任務處理廣播訊息
        asyncio.create_task(self._handle_broadcast_messages())

    async def _handle_broadcast_messages(self):
        """處理來自其他服務器的廣播訊息"""
        async for message in self.pubsub.listen():
            if message["type"] == "message":
                try:
                    data = json.loads(message["data"])
                    room_id = data["room_id"]
                    sender_server = data.get("sender_server")
                    
                    # 避免重複廣播
                    if sender_server != self.server_id:
                        await self._broadcast_to_local_room(room_id, data)
                        
                except (json.JSONDecodeError, KeyError):
                    continue

    async def connect(
        self, 
        websocket: WebSocket, 
        room_id: str, 
        user_info: dict
    ):
        """建立 WebSocket 連線"""
        await websocket.accept()
        
        # 本機連線管理
        if room_id not in self.local_connections:
            self.local_connections[room_id] = set()
        
        self.local_connections[room_id].add(websocket)
        self.websocket_to_user[websocket] = {
            **user_info,
            "room_id": room_id
        }
        
        # Redis 全域狀態
        await self.redis.sadd(f"room:{room_id}:users", user_info["user_id"])
        await self.redis.hset(
            f"user:{user_info['user_id']}", 
            mapping={
                "server_id": self.server_id,
                "room_id": room_id,
                "username": user_info["username"]
            }
        )
        
        # 廣播用戶加入
        await self.broadcast_to_room(room_id, {
            "message_type": "user_join",
            "user_id": user_info["user_id"],
            "username": user_info["username"],
            "content": f"{user_info['username']} 加入了聊天室"
        })

    async def disconnect(self, websocket: WebSocket):
        """斷開 WebSocket 連線"""
        user_info = self.websocket_to_user.get(websocket)
        if not user_info:
            return
        
        room_id = user_info["room_id"]
        user_id = user_info["user_id"]
        
        # 清理本機連線
        if room_id in self.local_connections:
            self.local_connections[room_id].discard(websocket)
            if not self.local_connections[room_id]:
                del self.local_connections[room_id]
        
        del self.websocket_to_user[websocket]
        
        # 清理 Redis 狀態
        await self.redis.srem(f"room:{room_id}:users", user_id)
        await self.redis.delete(f"user:{user_id}")
        
        # 廣播用戶離開
        await self.broadcast_to_room(room_id, {
            "message_type": "user_leave",
            "user_id": user_id,
            "username": user_info["username"],
            "content": f"{user_info['username']} 離開了聊天室"
        })

    async def broadcast_to_room(self, room_id: str, message: dict):
        """廣播訊息到房間（包含跨服務器）"""
        
        # 添加發送者服務器標識
        message["sender_server"] = self.server_id
        message["room_id"] = room_id
        
        # 本機廣播
        await self._broadcast_to_local_room(room_id, message)
        
        # 跨服務器廣播
        await self.redis.publish(
            "chatroom:broadcast", 
            json.dumps(message, default=str)
        )

    async def _broadcast_to_local_room(self, room_id: str, message: dict):
        """廣播到本機房間連線"""
        if room_id not in self.local_connections:
            return
        
        message_str = json.dumps(message, default=str)
        disconnected = []
        
        for websocket in list(self.local_connections[room_id]):
            try:
                await websocket.send_text(message_str)
            except Exception:
                disconnected.append(websocket)
        
        # 清理斷開的連線
        for ws in disconnected:
            await self.disconnect(ws)

    async def get_room_user_count(self, room_id: str) -> int:
        """獲取房間總人數（跨服務器）"""
        return await self.redis.scard(f"room:{room_id}:users")

    async def get_room_users(self, room_id: str) -> List[dict]:
        """獲取房間使用者列表"""
        user_ids = await self.redis.smembers(f"room:{room_id}:users")
        users = []
        
        for user_id in user_ids:
            user_data = await self.redis.hgetall(f"user:{user_id}")
            if user_data:
                users.append({
                    "user_id": user_id,
                    "username": user_data.get("username", ""),
                    "server_id": user_data.get("server_id", "")
                })
        
        return users
```

#### 2. 連線數限制和負載平衡

```python
# middleware/connection_limiter.py
from fastapi import HTTPException, status
from redis.asyncio import Redis

class ConnectionLimiter:
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        
        # 限制設定
        self.MAX_CONNECTIONS_PER_ROOM = 100
        self.MAX_CONNECTIONS_PER_USER = 3
        self.MAX_TOTAL_CONNECTIONS = 1000

    async def check_connection_limits(
        self, 
        room_id: str, 
        user_id: str
    ) -> bool:
        """檢查連線限制"""
        
        # 1. 檢查房間人數限制
        room_count = await self.redis.scard(f"room:{room_id}:users")
        if room_count >= self.MAX_CONNECTIONS_PER_ROOM:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="房間已滿"
            )
        
        # 2. 檢查使用者連線數
        user_connections = await self.redis.hlen(f"user:{user_id}:connections")
        if user_connections >= self.MAX_CONNECTIONS_PER_USER:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="超過同時連線數限制"
            )
        
        # 3. 檢查系統總連線數
        total_connections = await self.redis.get("system:total_connections")
        if total_connections and int(total_connections) >= self.MAX_TOTAL_CONNECTIONS:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="系統連線數已達上限，請稍後再試"
            )
        
        return True

    async def register_connection(self, user_id: str, connection_id: str):
        """註冊新連線"""
        await self.redis.hset(
            f"user:{user_id}:connections", 
            connection_id, 
            "active"
        )
        await self.redis.incr("system:total_connections")

    async def unregister_connection(self, user_id: str, connection_id: str):
        """註銷連線"""
        await self.redis.hdel(f"user:{user_id}:connections", connection_id)
        await self.redis.decr("system:total_connections")
```

### Rate Limiting 實作

```python
# middleware/rate_limiter.py
import time
from typing import Optional
from redis.asyncio import Redis
from fastapi import HTTPException, Request, status
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

# 建立 Redis 基礎的 Rate Limiter
def create_limiter(redis_client: Redis) -> Limiter:
    return Limiter(
        key_func=get_remote_address,
        storage_uri=f"redis://{redis_client.connection_pool.connection_kwargs['host']}:{redis_client.connection_pool.connection_kwargs['port']}"
    )

class WebSocketRateLimiter:
    """WebSocket 專用的 Rate Limiter"""
    
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        
        # 限制設定
        self.MESSAGE_LIMIT_PER_MINUTE = 30
        self.MESSAGE_LIMIT_PER_HOUR = 500
        self.IMAGE_LIMIT_PER_HOUR = 20

    async def check_message_rate_limit(
        self, 
        user_id: str, 
        message_type: str = "text"
    ) -> bool:
        """檢查訊息發送頻率限制"""
        
        current_time = int(time.time())
        minute_key = f"rate_limit:user:{user_id}:messages:{current_time // 60}"
        hour_key = f"rate_limit:user:{user_id}:messages:{current_time // 3600}"
        
        # 檢查每分鐘限制
        minute_count = await self.redis.incr(minute_key)
        if minute_count == 1:
            await self.redis.expire(minute_key, 60)
        
        if minute_count > self.MESSAGE_LIMIT_PER_MINUTE:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="發送訊息過於頻繁，請稍後再試"
            )
        
        # 檢查每小時限制
        hour_count = await self.redis.incr(hour_key)
        if hour_count == 1:
            await self.redis.expire(hour_key, 3600)
        
        limit = self.IMAGE_LIMIT_PER_HOUR if message_type == "image" else self.MESSAGE_LIMIT_PER_HOUR
        if hour_count > limit:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"每小時{message_type}訊息數量已達上限"
            )
        
        return True

# 在路由中使用 Rate Limiting
# routers/messages.py
from slowapi import Limiter
from app.middleware.rate_limiter import create_limiter, WebSocketRateLimiter

limiter = create_limiter(redis_client)

@router.post("/messages")
@limiter.limit("10/minute")  # API 限制
async def send_message(
    request: Request,
    message_data: MessageCreate,
    current_user: UserModel = Depends(get_current_user)
):
    # 訊息處理邏輯
    pass
```

### 分頁和查詢優化

```python
# routers/messages.py - 優化版本
from fastapi import Query, Depends
from datetime import datetime
from typing import Optional

@router.get("/messages/{room_id}")
async def get_room_messages(
    room_id: str,
    limit: int = Query(20, le=100, description="每頁訊息數量，最多100"),
    before: Optional[str] = Query(None, description="查詢此時間之前的訊息"),
    message_type: Optional[str] = Query(None, description="訊息類型篩選"),
    current_user: UserModel = Depends(get_current_user)
):
    """高效能分頁訊息查詢"""
    
    # 解析時間參數
    before_time = None
    if before:
        try:
            before_time = datetime.fromisoformat(before.replace('Z', '+00:00'))
        except ValueError:
            raise HTTPException(
                status_code=400, 
                detail="無效的時間格式"
            )
    
    # 使用服務層進行快取查詢
    messages = await message_service.get_room_messages(
        room_id=room_id,
        limit=limit,
        before_time=before_time,
        message_type=message_type
    )
    
    return {
        "messages": messages,
        "has_more": len(messages) == limit,
        "next_before": messages[-1].created_at.isoformat() if messages else None
    }
```

### 效能監控

```python
# middleware/performance_monitor.py
import time
import asyncio
from typing import Callable
from fastapi import Request, Response
from prometheus_client import Counter, Histogram, Gauge
import structlog

logger = structlog.get_logger()

# Prometheus 指標
REQUEST_COUNT = Counter(
    'http_requests_total', 
    'Total HTTP requests', 
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint']
)

WEBSOCKET_CONNECTIONS = Gauge(
    'websocket_connections_active',
    'Active WebSocket connections',
    ['room_id']
)

DATABASE_QUERY_DURATION = Histogram(
    'database_query_duration_seconds',
    'Database query duration',
    ['collection', 'operation']
)

async def performance_middleware(request: Request, call_next: Callable):
    """效能監控中間件"""
    start_time = time.time()
    
    # 執行請求
    response = await call_next(request)
    
    # 記錄指標
    duration = time.time() - start_time
    
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    REQUEST_DURATION.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(duration)
    
    # 記錄慢請求
    if duration > 1.0:  # 超過1秒的請求
        logger.warning(
            "Slow request detected",
            method=request.method,
            path=request.url.path,
            duration=duration,
            status=response.status_code
        )
    
    return response

# 資料庫查詢監控裝飾器
def monitor_db_query(collection: str, operation: str):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                DATABASE_QUERY_DURATION.labels(
                    collection=collection,
                    operation=operation
                ).observe(duration)
        return wrapper
    return decorator

# 使用範例
class MessageRepository:
    @monitor_db_query("messages", "find")
    async def get_recent_messages(self, room_id: str, limit: int):
        # 查詢實作
        pass
```

## 常見問題

### Q: WebSocket 連線如何處理認證？

A: 由於瀏覽器 WebSocket API 的限制，我們通過查詢參數傳遞 token：

```javascript
// 前端連線方式
const ws = new WebSocket(`ws://localhost:8000/ws/room1?token=${accessToken}`)
```

### Q: 如何處理 WebSocket 斷線重連？

A: 在前端實作指數退避重連機制：

```javascript
class WebSocketManager {
    connect(roomId, token) {
        this.ws = new WebSocket(`ws://localhost:8000/ws/${roomId}?token=${token}`)
        
        this.ws.onclose = (event) => {
            if (event.code !== 1000) {
                setTimeout(() => this.connect(roomId, token), this.retryDelay)
                this.retryDelay *= 2 // 指數退避
            }
        }
    }
}
```

### Q: 如何優化大量並發 WebSocket 連線？

A: 
1. 使用 Redis 進行跨服務器訊息廣播
2. 實作連線池和負載平衡
3. 定期清理斷開的連線
4. 使用心跳機制檢測死連線

### Q: GridFS 效能如何優化？

A:
1. 為 GridFS 建立適當的索引
2. 使用 CDN 快取靜態圖片
3. 實作圖片壓縮和多尺寸版本
4. 考慮使用外部對象存儲（如 S3）

這個後端指南提供了完整的 FastAPI 聊天室實作，涵蓋了認證、WebSocket、檔案上傳等核心功能。