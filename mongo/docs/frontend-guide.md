# SvelteKit 前端開發指南

這是聊天室應用前端的詳細開發指南，使用 SvelteKit 構建現代化響應式用戶界面。

## 目錄

- [為什麼選擇 SvelteKit](#為什麼選擇-sveltekit)
- [環境設定](#環境設定)
- [專案結構](#專案結構)
- [狀態管理](#狀態管理)
- [認證系統](#認證系統)
- [WebSocket 整合](#websocket-整合)
- [核心組件](#核心組件)
- [圖片上傳](#圖片上傳)
- [響應式設計](#響應式設計)
- [部署](#部署)

## 為什麼選擇 SvelteKit

### 優勢分析

| 特性 | SvelteKit | Vue 3 | React | 原生 JS |
|-----|-----------|-------|-------|---------|
| 學習曲線 | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐ |
| 開發速度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| 效能 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 生態系統 | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐ |
| 聊天室適配 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |

### 關鍵優勢

- **編譯時優化**：打包體積小，運行速度快
- **內建響應式**：完美適合聊天室的即時更新需求
- **簡潔語法**：學習成本低，開發效率高
- **現代開發體驗**：內建熱重載、TypeScript 支援、檔案系統路由

## 環境設定

### 建立專案

```bash
# 建立 SvelteKit 專案
npm create svelte@latest chatroom-frontend
cd chatroom-frontend

# 安裝基礎依賴
npm install

# 安裝額外套件
npm install -D tailwindcss autoprefixer @tailwindcss/typography
npm install lucide-svelte js-cookie

# 設定 Tailwind CSS
npx tailwindcss init -p
```

### 套件依賴

```json
{
  "dependencies": {
    "lucide-svelte": "^0.400.0",
    "js-cookie": "^3.0.5"
  },
  "devDependencies": {
    "@tailwindcss/typography": "^0.5.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.0"
  }
}
```

### Tailwind 配置

```javascript
// tailwind.config.js
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {
      colors: {
        'chat': {
          'primary': '#2563eb',
          'secondary': '#64748b',
          'background': '#f8fafc',
          'bubble': '#ffffff',
          'own-bubble': '#2563eb',
        }
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
```

## 專案結構

```
frontend/
├── src/
│   ├── routes/
│   │   ├── +layout.svelte          # 全域佈局
│   │   ├── +page.svelte            # 首頁（重導向）
│   │   ├── auth/
│   │   │   ├── +layout.svelte      # 認證頁面佈局
│   │   │   ├── login/+page.svelte  # 登入頁面
│   │   │   └── register/+page.svelte # 註冊頁面
│   │   └── chat/
│   │       ├── +layout.svelte      # 聊天室佈局
│   │       ├── rooms/+page.svelte  # 房間列表
│   │       └── [roomId]/+page.svelte # 聊天室主頁
│   ├── lib/
│   │   ├── components/
│   │   │   ├── ui/                 # 基礎 UI 組件
│   │   │   ├── chat/               # 聊天相關組件
│   │   │   └── layout/             # 佈局組件
│   │   ├── stores/
│   │   │   ├── auth.js             # 認證狀態
│   │   │   ├── chat.js             # 聊天狀態
│   │   │   └── websocket.js        # WebSocket 管理
│   │   ├── utils/
│   │   │   ├── api.js              # API 呼叫封裝
│   │   │   └── config.js           # 配置管理
│   │   └── styles/
│   │       └── app.css             # 全域樣式
│   └── app.html                    # HTML 模板
├── static/                         # 靜態資源
├── package.json
└── svelte.config.js
```

## 狀態管理

### 認證狀態 (stores/auth.js)

```javascript
import { writable, derived } from 'svelte/store'
import { browser } from '$app/environment'
import Cookies from 'js-cookie'

// 基礎狀態
export const user = writable(null)
export const token = writable(null)

// 衍生狀態
export const isAuthenticated = derived(
  [user, token],
  ([$user, $token]) => !!$user && !!$token
)

// Token 持久化
if (browser) {
  const savedToken = localStorage.getItem('access_token')
  
  if (savedToken) {
    token.set(savedToken)
    // 驗證 token 有效性
    validateToken(savedToken)
  }
}

// 認證函數
export const login = async (credentials) => {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    })
    
    const data = await response.json()
    
    if (response.ok) {
      user.set(data.user)
      token.set(data.access_token)
      
      // 存儲 token
      localStorage.setItem('access_token', data.access_token)
      
      return { success: true }
    }
    
    return { success: false, error: data.detail }
  } catch (error) {
    return { success: false, error: '網路連線錯誤' }
  }
}

export const logout = () => {
  user.set(null)
  token.set(null)
  
  localStorage.removeItem('access_token')
}

async function validateToken(token) {
  try {
    const response = await fetch('/api/auth/me', {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    
    if (response.ok) {
      const userData = await response.json()
      user.set(userData)
    } else {
      logout()
    }
  } catch (error) {
    logout()
  }
}
```

### 聊天狀態 (stores/chat.js)

```javascript
import { writable, derived } from 'svelte/store'

// 基礎狀態
export const currentRoom = writable(null)
export const messages = writable([])
export const onlineUsers = writable([])
export const rooms = writable([])
export const typing = writable(new Set())

// 衍生狀態
export const currentRoomMessages = derived(
  [messages, currentRoom],
  ([$messages, $currentRoom]) => {
    if (!$currentRoom) return []
    return $messages.filter(msg => msg.room_id === $currentRoom.id)
  }
)

// 訊息管理函數
export const addMessage = (message) => {
  messages.update(msgs => {
    // 防止重複訊息
    const exists = msgs.find(m => m.id === message.id)
    if (exists) return msgs
    
    // 保持訊息順序
    const newMsgs = [...msgs, message]
    return newMsgs.sort((a, b) => 
      new Date(a.created_at) - new Date(b.created_at)
    )
  })
}

export const clearMessages = () => {
  messages.set([])
}

export const setCurrentRoom = (room) => {
  currentRoom.set(room)
  clearMessages() // 切換房間時清空訊息
}
```

### WebSocket 管理 (stores/websocket.js)

```javascript
import { writable } from 'svelte/store'
import { addMessage, onlineUsers } from './chat.js'
import { token } from './auth.js'

export const wsConnection = writable(null)
export const wsConnected = writable(false)

class WebSocketManager {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
    this.currentRoomId = null
    this.currentToken = null
  }

  connect(roomId, authToken) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.disconnect()
    }

    this.currentRoomId = roomId
    this.currentToken = authToken
    
    const wsUrl = `ws://localhost:8000/ws/${roomId}?token=${authToken}`
    this.ws = new WebSocket(wsUrl)
    
    this.ws.onopen = () => {
      console.log('WebSocket 連線建立')
      wsConnected.set(true)
      this.reconnectAttempts = 0
    }
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleMessage(data)
    }
    
    this.ws.onclose = (event) => {
      console.log('WebSocket 連線關閉', event.code)
      wsConnected.set(false)
      
      // 自動重連（非正常關閉）
      if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect()
      }
    }
    
    this.ws.onerror = (error) => {
      console.error('WebSocket 錯誤:', error)
      wsConnected.set(false)
    }
    
    wsConnection.set(this)
  }

  handleMessage(data) {
    switch (data.message_type) {
      case 'text':
      case 'image':
        addMessage({
          id: data.message_id,
          room_id: data.room_id,
          user_id: data.user_id,
          username: data.username,
          message_type: data.message_type,
          content: data.content,
          image_id: data.image_id,
          created_at: data.created_at
        })
        break
        
      case 'user_join':
      case 'user_leave':
        // 顯示系統訊息
        addMessage({
          id: `system_${Date.now()}`,
          room_id: data.room_id || this.currentRoomId,
          user_id: 'system',
          username: 'System',
          message_type: 'system',
          content: data.content,
          created_at: data.created_at
        })
        break
    }
  }

  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
      return true
    }
    return false
  }

  sendMessage(content) {
    return this.send({
      message_type: 'text',
      content: content
    })
  }

  sendImage(imageId) {
    return this.send({
      message_type: 'image',
      image_id: imageId
    })
  }

  scheduleReconnect() {
    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    setTimeout(() => {
      console.log(`嘗試重連 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      this.connect(this.currentRoomId, this.currentToken)
    }, delay)
  }

  disconnect() {
    if (this.ws) {
      this.ws.close(1000, '正常關閉')
      this.ws = null
    }
    wsConnected.set(false)
    wsConnection.set(null)
  }
}

export const wsManager = new WebSocketManager()
```

## 認證系統

### 登入頁面 (routes/auth/login/+page.svelte)

```svelte
<script>
  import { goto } from '$app/navigation'
  import { login, isAuthenticated } from '$lib/stores/auth.js'
  import { onMount } from 'svelte'
  
  let username = ''
  let password = ''
  let loading = false
  let error = ''
  
  // 如果已登入，重導向到聊天室
  onMount(() => {
    const unsubscribe = isAuthenticated.subscribe(authenticated => {
      if (authenticated) {
        goto('/chat/rooms')
      }
    })
    
    return unsubscribe
  })
  
  async function handleLogin() {
    if (!username || !password) {
      error = '請填寫用戶名和密碼'
      return
    }
    
    loading = true
    error = ''
    
    const result = await login({ username, password })
    
    if (result.success) {
      goto('/chat/rooms')
    } else {
      error = result.error
    }
    
    loading = false
  }
</script>

<div class="min-h-screen bg-gray-50 flex items-center justify-center">
  <div class="max-w-md w-full space-y-8">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        登入聊天室
      </h2>
    </div>
    
    <form class="mt-8 space-y-6" on:submit|preventDefault={handleLogin}>
      <div class="rounded-md shadow-sm -space-y-px">
        <div>
          <input
            bind:value={username}
            type="text"
            required
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="用戶名"
          />
        </div>
        <div>
          <input
            bind:value={password}
            type="password"
            required
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="密碼"
          />
        </div>
      </div>

      {#if error}
        <div class="text-red-600 text-sm text-center">
          {error}
        </div>
      {/if}

      <div>
        <button
          type="submit"
          disabled={loading}
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? '登入中...' : '登入'}
        </button>
      </div>

      <div class="text-center">
        <a href="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">
          沒有帳號？註冊新帳號
        </a>
      </div>
    </form>
  </div>
</div>
```

### 路由守衛 (+layout.svelte)

```svelte
<script>
  import { goto } from '$app/navigation'
  import { page } from '$app/stores'
  import { isAuthenticated } from '$lib/stores/auth.js'
  import { onMount } from 'svelte'
  
  let mounted = false
  
  onMount(() => {
    mounted = true
  })
  
  // 保護聊天室路由
  $: if (mounted && $page.route.id?.startsWith('/chat') && !$isAuthenticated) {
    goto('/auth/login')
  }
  
  // 已登入用戶訪問認證頁面時重導向
  $: if (mounted && $page.route.id?.startsWith('/auth') && $isAuthenticated) {
    goto('/chat/rooms')
  }
</script>

<main>
  <slot />
</main>
```

## WebSocket 整合

### 聊天室主頁 (routes/chat/[roomId]/+page.svelte)

```svelte
<script>
  import { onMount, onDestroy } from 'svelte'
  import { page } from '$app/stores'
  import { wsManager, wsConnected } from '$lib/stores/websocket.js'
  import { token } from '$lib/stores/auth.js'
  import { currentRoomMessages, setCurrentRoom } from '$lib/stores/chat.js'
  import ChatMessage from '$lib/components/chat/ChatMessage.svelte'
  import ChatInput from '$lib/components/chat/ChatInput.svelte'
  
  $: roomId = $page.params.roomId
  let messagesContainer
  
  onMount(async () => {
    if (!$token) return
    
    // 設定當前房間
    setCurrentRoom({ id: roomId })
    
    // 建立 WebSocket 連線
    wsManager.connect(roomId, $token)
    
    // 載入歷史訊息
    await loadMessages()
  })
  
  onDestroy(() => {
    wsManager.disconnect()
  })
  
  async function loadMessages() {
    try {
      const response = await fetch(`/api/rooms/${roomId}/messages?limit=50`, {
        headers: { 'Authorization': `Bearer ${$token}` }
      })
      
      if (response.ok) {
        const data = await response.json()
        // 將歷史訊息加入到 store
        data.messages.forEach(msg => {
          addMessage(msg)
        })
      }
    } catch (error) {
      console.error('載入訊息失敗:', error)
    }
  }
  
  function handleSendMessage(event) {
    const { content } = event.detail
    wsManager.sendMessage(content)
  }
  
  function handleSendImage(event) {
    const { imageId } = event.detail
    wsManager.sendImage(imageId)
  }
  
  // 自動捲動到底部
  $: if ($currentRoomMessages && messagesContainer) {
    setTimeout(() => {
      messagesContainer.scrollTop = messagesContainer.scrollHeight
    }, 100)
  }
</script>

<div class="flex flex-col h-full">
  <!-- 聊天室標題 -->
  <div class="bg-white border-b border-gray-200 p-4">
    <h1 class="text-lg font-semibold">聊天室 {roomId}</h1>
    <div class="text-sm text-gray-500">
      {#if $wsConnected}
        <span class="text-green-600">● 已連線</span>
      {:else}
        <span class="text-red-600">● 連線中...</span>
      {/if}
    </div>
  </div>
  
  <!-- 訊息區域 -->
  <div 
    bind:this={messagesContainer}
    class="flex-1 overflow-y-auto p-4 space-y-4"
  >
    {#each $currentRoomMessages as message (message.id)}
      <ChatMessage {message} />
    {/each}
    
    {#if $currentRoomMessages.length === 0}
      <div class="text-center text-gray-500 mt-8">
        還沒有訊息，開始聊天吧！
      </div>
    {/if}
  </div>
  
  <!-- 輸入區域 -->
  <ChatInput 
    disabled={!$wsConnected}
    on:sendMessage={handleSendMessage}
    on:sendImage={handleSendImage}
  />
</div>
```

## 核心組件

### 訊息組件 (components/chat/ChatMessage.svelte)

```svelte
<script>
  import { user } from '$lib/stores/auth.js'
  
  export let message
  
  $: isOwnMessage = $user?.id === message.user_id
  $: isSystemMessage = message.message_type === 'system'
  $: isImageMessage = message.message_type === 'image'
  
  function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString('zh-TW', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }
</script>

<div class="message-container" class:own-message={isOwnMessage} class:system-message={isSystemMessage}>
  {#if isSystemMessage}
    <!-- 系統訊息 -->
    <div class="text-center py-2">
      <span class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
        {message.content}
      </span>
    </div>
  {:else}
    <!-- 一般訊息 -->
    <div class="flex items-start gap-3 p-3" class:flex-row-reverse={isOwnMessage}>
      <!-- 頭像 -->
      <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
           class:bg-blue-500={!isOwnMessage}
           class:bg-green-500={isOwnMessage}>
        {message.username?.charAt(0)?.toUpperCase() || 'U'}
      </div>
      
      <!-- 訊息內容 -->
      <div class="flex-1 max-w-md" class:text-right={isOwnMessage}>
        {#if !isOwnMessage}
          <div class="text-sm text-gray-600 mb-1">{message.username}</div>
        {/if}
        
        <div class="message-bubble" class:own-bubble={isOwnMessage}>
          {#if isImageMessage}
            <img 
              src="/api/images/{message.image_id}" 
              alt="聊天圖片"
              class="max-w-xs max-h-64 rounded-lg"
              loading="lazy"
            />
          {:else}
            <p class="text-sm whitespace-pre-wrap break-words">{message.content}</p>
          {/if}
          
          <div class="text-xs opacity-70 mt-1">
            {formatTime(message.created_at)}
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .message-bubble {
    @apply bg-white border border-gray-200 rounded-lg p-3 shadow-sm;
  }
  
  .own-bubble {
    @apply bg-blue-500 text-white border-blue-500;
  }
  
  .system-message {
    @apply py-2;
  }
</style>
```

### 輸入組件 (components/chat/ChatInput.svelte)

```svelte
<script>
  import { createEventDispatcher } from 'svelte'
  import { Send, Image } from 'lucide-svelte'
  import ImageUpload from './ImageUpload.svelte'
  
  export let disabled = false
  
  const dispatch = createEventDispatcher()
  
  let message = ''
  let showImageUpload = false
  
  function handleSubmit() {
    if (message.trim() && !disabled) {
      dispatch('sendMessage', { content: message.trim() })
      message = ''
    }
  }
  
  function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      handleSubmit()
    }
  }
  
  function handleImageUpload(event) {
    const { imageId } = event.detail
    dispatch('sendImage', { imageId })
    showImageUpload = false
  }
</script>

<div class="bg-white border-t border-gray-200">
  {#if showImageUpload}
    <div class="p-4 border-b border-gray-200">
      <ImageUpload 
        on:uploaded={handleImageUpload} 
        on:cancel={() => showImageUpload = false}
      />
    </div>
  {/if}
  
  <div class="flex items-end gap-2 p-4">
    <!-- 圖片上傳按鈕 -->
    <button 
      type="button"
      class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
      on:click={() => showImageUpload = !showImageUpload}
      title="上傳圖片"
    >
      <Image size={20} />
    </button>
    
    <!-- 文字輸入 -->
    <div class="flex-1">
      <textarea
        bind:value={message}
        on:keydown={handleKeyDown}
        placeholder="輸入訊息..."
        disabled={disabled}
        rows="1"
        class="w-full p-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        style="min-height: 40px; max-height: 120px;"
      ></textarea>
    </div>
    
    <!-- 發送按鈕 -->
    <button 
      type="submit"
      class="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:bg-gray-300"
      class:disabled
      on:click={handleSubmit}
      title="發送訊息"
    >
      <Send size={20} />
    </button>
  </div>
</div>
```

## 圖片上傳

### 上傳組件 (components/chat/ImageUpload.svelte)

```svelte
<script>
  import { createEventDispatcher } from 'svelte'
  import { Upload, X, AlertCircle } from 'lucide-svelte'
  import { token } from '$lib/stores/auth.js'
  
  const dispatch = createEventDispatcher()
  
  let dragOver = false
  let uploading = false
  let error = null
  let preview = null
  let fileInput
  
  const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
  const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  
  function handleDragOver(event) {
    event.preventDefault()
    dragOver = true
  }
  
  function handleDragLeave(event) {
    event.preventDefault()
    dragOver = false
  }
  
  function handleDrop(event) {
    event.preventDefault()
    dragOver = false
    
    const files = Array.from(event.dataTransfer.files)
    if (files.length > 0) {
      handleFile(files[0])
    }
  }
  
  function handleFileSelect(event) {
    const files = Array.from(event.target.files)
    if (files.length > 0) {
      handleFile(files[0])
    }
  }
  
  function handleFile(file) {
    error = null
    
    // 驗證檔案類型
    if (!ALLOWED_TYPES.includes(file.type)) {
      error = '僅支援 JPEG、PNG、GIF、WebP 格式的圖片'
      return
    }
    
    // 驗證檔案大小
    if (file.size > MAX_FILE_SIZE) {
      error = '圖片大小不能超過 10MB'
      return
    }
    
    // 顯示預覽
    const reader = new FileReader()
    reader.onload = (e) => {
      preview = e.target.result
    }
    reader.readAsDataURL(file)
    
    // 上傳檔案
    uploadFile(file)
  }
  
  async function uploadFile(file) {
    uploading = true
    error = null
    
    try {
      const formData = new FormData()
      formData.append('image', file)
      
      const response = await fetch('/api/upload/image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${$token}`
        },
        body: formData
      })
      
      if (response.ok) {
        const data = await response.json()
        dispatch('uploaded', { imageId: data.file_id })
      } else {
        const errorData = await response.json()
        error = errorData.detail || '上傳失敗'
      }
    } catch (err) {
      error = '網路連線錯誤'
    } finally {
      uploading = false
    }
  }
  
  function clearPreview() {
    preview = null
    error = null
    if (fileInput) {
      fileInput.value = ''
    }
  }
  
  function handleCancel() {
    clearPreview()
    dispatch('cancel')
  }
</script>

<div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
  <div class="flex items-center justify-between p-4 border-b border-gray-200">
    <h3 class="text-lg font-medium">上傳圖片</h3>
    <button on:click={handleCancel} class="p-1 text-gray-400 hover:text-gray-600 rounded">
      <X size={20} />
    </button>
  </div>
  
  {#if !preview}
    <div 
      class="p-8 border-2 border-dashed border-gray-300 text-center cursor-pointer transition-colors"
      class:border-blue-500={dragOver}
      class:bg-blue-50={dragOver}
      on:dragover={handleDragOver}
      on:dragleave={handleDragLeave}
      on:drop={handleDrop}
    >
      <div class="flex flex-col items-center">
        <Upload size={48} class="text-gray-400 mb-4" />
        <p class="text-lg mb-2">拖拽圖片到此處</p>
        <p class="text-sm text-gray-500 mb-4">或</p>
        <button 
          type="button"
          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          on:click={() => fileInput.click()}
        >
          選擇檔案
        </button>
        <input 
          bind:this={fileInput}
          type="file" 
          accept="image/*"
          on:change={handleFileSelect}
          class="hidden"
        />
      </div>
      
      <div class="mt-4 pt-4 border-t border-gray-200">
        <p class="text-xs text-gray-500">
          支援 JPEG、PNG、GIF、WebP 格式，最大 10MB
        </p>
      </div>
    </div>
  {:else}
    <div class="relative p-4">
      <img src={preview} alt="預覽" class="w-full max-w-md mx-auto rounded-lg" />
      
      {#if uploading}
        <div class="absolute inset-0 bg-black bg-opacity-50 flex flex-col items-center justify-center text-white rounded-lg">
          <div class="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <p class="text-sm mt-2">上傳中...</p>
        </div>
      {/if}
      
      <div class="flex justify-center gap-2 mt-4">
        <button on:click={clearPreview} class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
          重新選擇
        </button>
      </div>
    </div>
  {/if}
  
  {#if error}
    <div class="flex items-center gap-2 p-3 bg-red-50 text-red-700 text-sm border-t border-red-200">
      <AlertCircle size={16} />
      <span>{error}</span>
    </div>
  {/if}
</div>
```

## 響應式設計

### 主佈局 (routes/chat/+layout.svelte)

```svelte
<script>
  import { page } from '$app/stores'
  import { isAuthenticated } from '$lib/stores/auth.js'
  import Sidebar from '$lib/components/layout/Sidebar.svelte'
  import Header from '$lib/components/layout/Header.svelte'
  
  let sidebarOpen = false
  
  // 手機版自動收起側邊欄
  $: if ($page.route.id?.includes('[roomId]')) {
    sidebarOpen = false
  }
</script>

{#if $isAuthenticated}
  <div class="chat-layout">
    <!-- 桌面版側邊欄 -->
    <div class="sidebar-desktop">
      <Sidebar />
    </div>
    
    <!-- 手機版側邊欄 -->
    {#if sidebarOpen}
      <div class="sidebar-mobile">
        <Sidebar on:close={() => sidebarOpen = false} />
      </div>
      <div class="overlay" on:click={() => sidebarOpen = false}></div>
    {/if}
    
    <!-- 主內容區域 -->
    <div class="main-content">
      <Header 
        on:toggleSidebar={() => sidebarOpen = !sidebarOpen}
        showMenuButton={true}
      />
      
      <main class="chat-main">
        <slot />
      </main>
    </div>
  </div>
{:else}
  <!-- 未登入狀態 -->
  <div class="auth-layout">
    <slot />
  </div>
{/if}

<style>
  .chat-layout {
    @apply flex h-screen bg-gray-100;
  }
  
  .sidebar-desktop {
    @apply hidden md:flex w-80 bg-white border-r border-gray-200 flex-shrink-0;
  }
  
  .sidebar-mobile {
    @apply fixed inset-y-0 left-0 z-50 w-80 bg-white transform transition-transform duration-300 ease-in-out md:hidden;
  }
  
  .overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden;
  }
  
  .main-content {
    @apply flex-1 flex flex-col min-w-0;
  }
  
  .chat-main {
    @apply flex-1 overflow-hidden;
  }
  
  .auth-layout {
    @apply min-h-screen bg-gray-50;
  }
</style>
```

## 部署

### 環境配置

```javascript
// lib/utils/config.js
export const config = {
  apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  wsUrl: import.meta.env.VITE_WS_URL || 'ws://localhost:8000',
  appName: import.meta.env.VITE_APP_NAME || '聊天室',
}
```

### 建構設定

```bash
# 開發模式
npm run dev

# 建構生產版本
npm run build

# 預覽建構結果
npm run preview
```

### 部署到 Vercel

```bash
# 安裝 Vercel CLI
npm i -g vercel

# 部署
vercel

# 生產部署
vercel --prod
```

這個前端指南提供了完整的 SvelteKit 聊天室實作，涵蓋了認證、WebSocket、響應式設計等核心功能，讓您能夠快速建立現代化的即時通訊前端應用。