# 部署指南

這是聊天室應用的完整部署指南，涵蓋從本地開發到生產環境的各種部署策略。

## 目錄

- [部署策略概覽](#部署策略概覽)
- [本地開發環境](#本地開發環境)
- [Docker 配置](#docker-配置)
- [生產環境部署](#生產環境部署)
- [監控與維護](#監控與維護)
- [故障排除](#故障排除)

## 部署策略概覽

### 漸進式容器化策略

我們採用漸進式的容器化方案，讓開發者可以逐步從本地開發轉移到完全容器化環境：

1. **階段一**: 本地開發 (後端 + 前端在本機，資料庫服務 Docker 化)
2. **階段二**: 混合部署 (後端容器化，前端本地開發)
3. **階段三**: 完全容器化 (所有服務 Docker 化)
4. **階段四**: 生產部署 (Kubernetes 或 Docker Swarm)

### 環境類型

- **Development**: 本地開發環境
- **Staging**: 預發布測試環境
- **Production**: 生產環境

## 本地開發環境

### 階段一：資料庫服務容器化

僅將 MongoDB 和 Redis 容器化，後端和前端在本機開發。

#### docker-compose.dev.yml

```yaml
version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: chatroom-mongo-dev
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: chatroom
    volumes:
      - mongodb_data_dev:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - chatroom-network

  redis:
    image: redis:7.2-alpine
    container_name: chatroom-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data_dev:/data
    networks:
      - chatroom-network

  mongo-express:
    image: mongo-express:1.0.0
    container_name: chatroom-mongo-express-dev
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH: false
    depends_on:
      - mongodb
    networks:
      - chatroom-network

volumes:
  mongodb_data_dev:
  redis_data_dev:

networks:
  chatroom-network:
    driver: bridge
```

#### 啟動開發環境

```bash
# 啟動資料庫服務
docker-compose -f docker-compose.dev.yml up -d

# 後端開發 (在 backend/ 目錄)
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt

# 設定環境變數
export MONGODB_URL="*********************************************************************"
export REDIS_URL="redis://:redis123@localhost:6379/0"
export JWT_SECRET="your_super_secret_jwt_key_change_in_production"

# 啟動後端
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 前端開發 (在 frontend/ 目錄，另一個終端)
cd frontend
npm install
npm run dev
```

#### 開發腳本

創建 `scripts/dev.sh` 簡化開發流程：

```bash
#!/bin/bash

# 啟動開發環境腳本
set -e

echo "🚀 啟動聊天室開發環境..."

# 檢查 Docker 是否運行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未運行，請先啟動 Docker"
    exit 1
fi

# 啟動資料庫服務
echo "📦 啟動資料庫服務..."
docker-compose -f docker-compose.dev.yml up -d

# 等待服務就緒
echo "⏳ 等待服務啟動..."
sleep 10

# 檢查服務狀態
echo "🔍 檢查服務狀態..."
docker-compose -f docker-compose.dev.yml ps

echo "✅ 開發環境已啟動！"
echo ""
echo "🔗 服務連接："
echo "  - MongoDB: *********************************************************************"
echo "  - Redis: redis://:redis123@localhost:6379/0"
echo "  - Mongo Express: http://localhost:8081"
echo ""
echo "📝 接下來的步驟："
echo "  1. 在 backend/ 目錄啟動後端: ./scripts/start-backend.sh"
echo "  2. 在 frontend/ 目錄啟動前端: npm run dev"
```

#### MongoDB 初始化腳本

`scripts/mongo-init.js`:

```javascript
// 創建聊天室資料庫和初始用戶
db = db.getSiblingDB('chatroom');

// 創建集合和索引
db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "email": 1 }, { unique: true });

db.messages.createIndex({ "room_id": 1, "created_at": -1 });
db.messages.createIndex({ "user_id": 1, "created_at": -1 });

db.rooms.createIndex({ "name": 1 });
db.rooms.createIndex({ "owner_id": 1 });

// 創建預設聊天室
db.rooms.insertMany([
    {
        name: "一般討論",
        description: "歡迎大家在這裡討論各種話題",
        owner_id: "system",
        created_at: new Date(),
        is_private: false,
        max_members: 100
    },
    {
        name: "技術交流",
        description: "技術相關討論區",
        owner_id: "system", 
        created_at: new Date(),
        is_private: false,
        max_members: 50
    }
]);

print("✅ 聊天室資料庫初始化完成");
```

## Docker 配置

### 階段二：後端容器化

#### backend/Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    gcc \
    libmagic1 \
    && rm -rf /var/lib/apt/lists/*

# 複製需求檔案
COPY requirements.txt .

# 安裝 Python 依賴
RUN pip install --no-cache-dir -r requirements.txt

# 複製應用程式碼
COPY . .

# 創建非 root 用戶
RUN useradd --create-home --shell /bin/bash app && chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 啟動命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### docker-compose.hybrid.yml

```yaml
version: '3.8'

services:
  # 資料庫服務 (同 dev 配置)
  mongodb:
    image: mongo:7.0
    container_name: chatroom-mongo
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: chatroom
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - chatroom-network

  redis:
    image: redis:7.2-alpine
    container_name: chatroom-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    networks:
      - chatroom-network

  # 後端服務
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: chatroom-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      MONGODB_URL: *****************************************/chatroom?authSource=admin
      REDIS_URL: redis://:redis123@redis:6379/0
      JWT_SECRET: your_super_secret_jwt_key_change_in_production
      JWT_ALGORITHM: HS256
      JWT_EXPIRE_MINUTES: 30
      DEBUG: "true"
    depends_on:
      - mongodb
      - redis
    networks:
      - chatroom-network
    volumes:
      - ./backend:/app:ro  # 開發時掛載代碼
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # 資料庫管理界面
  mongo-express:
    image: mongo-express:1.0.0
    container_name: chatroom-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH: false
    depends_on:
      - mongodb
    networks:
      - chatroom-network

volumes:
  mongodb_data:
  redis_data:

networks:
  chatroom-network:
    driver: bridge
```

### 階段三：完全容器化

#### frontend/Dockerfile

```dockerfile
# 多階段構建
FROM node:18-alpine AS builder

WORKDIR /app

# 複製 package 檔案
COPY package*.json ./

# 安裝依賴
RUN npm ci --only=production

# 複製源代碼
COPY . .

# 構建應用
RUN npm run build

# 生產階段
FROM nginx:alpine

# 複製構建結果
COPY --from=builder /app/build /usr/share/nginx/html

# 複製 nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

CMD ["nginx", "-g", "daemon off;"]
```

#### frontend/nginx.conf

```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 基本設定
    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  65;
    types_hash_max_size 2048;

    # Gzip 壓縮
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        application/javascript
        application/json
        application/xml
        text/css
        text/javascript
        text/plain
        text/xml;

    server {
        listen 80;
        server_name localhost;
        
        root /usr/share/nginx/html;
        index index.html;

        # SPA 路由支援
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API 代理 (開發環境)
        location /api {
            proxy_pass http://backend:8000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # WebSocket 代理
        location /ws {
            proxy_pass http://backend:8000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 靜態資源快取
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 健康檢查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # 安全標頭
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";
    }
}
```

#### docker-compose.full.yml

```yaml
version: '3.8'

services:
  # 資料庫服務
  mongodb:
    image: mongo:7.0
    container_name: chatroom-mongo
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-password123}
      MONGO_INITDB_DATABASE: chatroom
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - chatroom-network
    # 生產環境不暴露端口
    # ports:
    #   - "27017:27017"

  redis:
    image: redis:7.2-alpine
    container_name: chatroom-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    networks:
      - chatroom-network

  # 後端服務
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: chatroom-backend
    restart: unless-stopped
    environment:
      MONGODB_URL: mongodb://admin:${MONGO_PASSWORD:-password123}@mongodb:27017/chatroom?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      JWT_SECRET: ${JWT_SECRET:-your_super_secret_jwt_key_change_in_production}
      JWT_ALGORITHM: HS256
      JWT_EXPIRE_MINUTES: 30
      DEBUG: "false"
    depends_on:
      - mongodb
      - redis
    networks:
      - chatroom-network
    # 內部通訊，不對外暴露
    expose:
      - "8000"

  # 前端服務
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: chatroom-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - chatroom-network

  # 資料庫管理 (僅開發環境)
  mongo-express:
    image: mongo-express:1.0.0
    container_name: chatroom-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_PASSWORD:-password123}
      ME_CONFIG_MONGODB_URL: mongodb://admin:${MONGO_PASSWORD:-password123}@mongodb:27017/
      ME_CONFIG_BASICAUTH: true
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_EXPRESS_PASSWORD:-admin123}
    depends_on:
      - mongodb
    networks:
      - chatroom-network
    profiles:
      - dev

volumes:
  mongodb_data:
  redis_data:

networks:
  chatroom-network:
    driver: bridge
```

## 生產環境部署

### 環境變數管理

創建 `.env.production`:

```bash
# 資料庫配置
MONGO_PASSWORD=your_strong_mongo_password
REDIS_PASSWORD=your_strong_redis_password

# JWT 配置
JWT_SECRET=your_super_secret_jwt_key_minimum_32_characters_long
JWT_EXPIRE_MINUTES=30

# 管理界面
MONGO_EXPRESS_PASSWORD=your_admin_password

# SSL 憑證 (如果使用)
SSL_CERT_PATH=/etc/ssl/certs/chatroom.crt
SSL_KEY_PATH=/etc/ssl/private/chatroom.key

# 監控配置
ENABLE_METRICS=true
METRICS_PORT=9090
```

### 高可用性配置

#### docker-compose.prod.yml

```yaml
version: '3.8'

services:
  # MongoDB 副本集
  mongo1:
    image: mongo:7.0
    container_name: chatroom-mongo1
    restart: unless-stopped
    command: ["--replSet", "rs0", "--bind_ip_all", "--port", "27017"]
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongo1_data:/data/db
    networks:
      - chatroom-network
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.labels.mongo-node == 1

  mongo2:
    image: mongo:7.0
    container_name: chatroom-mongo2
    restart: unless-stopped
    command: ["--replSet", "rs0", "--bind_ip_all", "--port", "27017"]
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongo2_data:/data/db
    networks:
      - chatroom-network
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.labels.mongo-node == 2

  mongo3:
    image: mongo:7.0
    container_name: chatroom-mongo3
    restart: unless-stopped
    command: ["--replSet", "rs0", "--bind_ip_all", "--port", "27017"]
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongo3_data:/data/db
    networks:
      - chatroom-network
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.labels.mongo-node == 3

  # Redis Sentinel 高可用
  redis-master:
    image: redis:7.2-alpine
    container_name: chatroom-redis-master
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_master_data:/data
    networks:
      - chatroom-network
    deploy:
      replicas: 1

  redis-slave:
    image: redis:7.2-alpine
    container_name: chatroom-redis-slave
    restart: unless-stopped
    command: redis-server --slaveof redis-master 6379 --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_slave_data:/data
    depends_on:
      - redis-master
    networks:
      - chatroom-network
    deploy:
      replicas: 1

  # 後端服務 (多實例)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    environment:
      MONGODB_URL: mongodb://admin:${MONGO_PASSWORD}@mongo1:27017,mongo2:27017,mongo3:27017/chatroom?replicaSet=rs0&authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis-master:6379/0
      JWT_SECRET: ${JWT_SECRET}
      JWT_ALGORITHM: HS256
      JWT_EXPIRE_MINUTES: ${JWT_EXPIRE_MINUTES:-30}
      DEBUG: "false"
      ENABLE_METRICS: ${ENABLE_METRICS:-true}
    depends_on:
      - mongo1
      - mongo2
      - mongo3
      - redis-master
    networks:
      - chatroom-network
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # 負載平衡器
  nginx:
    image: nginx:alpine
    container_name: chatroom-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./frontend/build:/usr/share/nginx/html:ro
    depends_on:
      - backend
    networks:
      - chatroom-network
    deploy:
      replicas: 2

volumes:
  mongo1_data:
  mongo2_data:
  mongo3_data:
  redis_master_data:
  redis_slave_data:

networks:
  chatroom-network:
    driver: overlay
    attachable: true
```

### 部署腳本

#### scripts/deploy.sh

```bash
#!/bin/bash

# 生產環境部署腳本
set -e

ENVIRONMENT=${1:-production}
COMPOSE_FILE="docker-compose.${ENVIRONMENT}.yml"

echo "🚀 部署聊天室應用到 ${ENVIRONMENT} 環境..."

# 檢查環境檔案
if [ ! -f ".env.${ENVIRONMENT}" ]; then
    echo "❌ 找不到環境檔案: .env.${ENVIRONMENT}"
    exit 1
fi

# 載入環境變數
source .env.${ENVIRONMENT}

# 檢查必要變數
required_vars=("MONGO_PASSWORD" "REDIS_PASSWORD" "JWT_SECRET")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ 缺少必要環境變數: $var"
        exit 1
    fi
done

# 構建鏡像
echo "🔨 構建應用鏡像..."
docker-compose -f ${COMPOSE_FILE} build --no-cache

# 停止舊服務
echo "🛑 停止舊服務..."
docker-compose -f ${COMPOSE_FILE} down

# 備份資料 (如果是生產環境)
if [ "$ENVIRONMENT" = "production" ]; then
    echo "💾 備份資料..."
    ./scripts/backup.sh
fi

# 啟動新服務
echo "🌟 啟動新服務..."
docker-compose -f ${COMPOSE_FILE} up -d

# 等待服務就緒
echo "⏳ 等待服務啟動..."
sleep 30

# 健康檢查
echo "🔍 執行健康檢查..."
./scripts/health-check.sh

echo "✅ 部署完成！"
echo ""
echo "🔗 服務狀態："
docker-compose -f ${COMPOSE_FILE} ps
```

#### scripts/health-check.sh

```bash
#!/bin/bash

# 健康檢查腳本
set -e

BACKEND_URL=${BACKEND_URL:-http://localhost:8000}
FRONTEND_URL=${FRONTEND_URL:-http://localhost}

echo "🔍 執行健康檢查..."

# 檢查後端 API
echo "  檢查後端 API..."
if curl -f "${BACKEND_URL}/health" > /dev/null 2>&1; then
    echo "  ✅ 後端 API 正常"
else
    echo "  ❌ 後端 API 異常"
    exit 1
fi

# 檢查前端
echo "  檢查前端..."
if curl -f "${FRONTEND_URL}/health" > /dev/null 2>&1; then
    echo "  ✅ 前端正常"
else
    echo "  ❌ 前端異常"
    exit 1
fi

# 檢查資料庫連線
echo "  檢查資料庫連線..."
if curl -f "${BACKEND_URL}/api/health/db" > /dev/null 2>&1; then
    echo "  ✅ 資料庫連線正常"
else
    echo "  ❌ 資料庫連線異常"
    exit 1
fi

echo "✅ 所有健康檢查通過！"
```

## 監控與維護

### 基本監控配置

#### docker-compose.monitoring.yml

```yaml
version: '3.8'

services:
  # Prometheus 監控
  prometheus:
    image: prom/prometheus:latest
    container_name: chatroom-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - chatroom-network

  # Grafana 儀表板
  grafana:
    image: grafana/grafana:latest
    container_name: chatroom-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - chatroom-network

  # Redis 監控
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: chatroom-redis-exporter
    restart: unless-stopped
    environment:
      REDIS_ADDR: redis://redis:6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    networks:
      - chatroom-network

volumes:
  prometheus_data:
  grafana_data:

networks:
  chatroom-network:
    external: true
```

### 備份策略

#### scripts/backup.sh

```bash
#!/bin/bash

# 資料備份腳本
set -e

BACKUP_DIR="/backup/chatroom"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="chatroom_backup_${DATE}"

echo "💾 開始備份資料..."

# 建立備份目錄
mkdir -p ${BACKUP_DIR}

# 備份 MongoDB
echo "  備份 MongoDB..."
docker exec chatroom-mongo mongodump \
    --username admin \
    --password ${MONGO_PASSWORD} \
    --authenticationDatabase admin \
    --db chatroom \
    --out /backup

# 複製備份檔案
docker cp chatroom-mongo:/backup ${BACKUP_DIR}/${BACKUP_NAME}_mongodb

# 備份 Redis
echo "  備份 Redis..."
docker exec chatroom-redis redis-cli \
    --rdb /data/dump.rdb \
    --auth ${REDIS_PASSWORD}

docker cp chatroom-redis:/data/dump.rdb ${BACKUP_DIR}/${BACKUP_NAME}_redis.rdb

# 壓縮備份
echo "  壓縮備份檔案..."
cd ${BACKUP_DIR}
tar -czf ${BACKUP_NAME}.tar.gz ${BACKUP_NAME}_*
rm -rf ${BACKUP_NAME}_*

echo "✅ 備份完成: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"

# 清理舊備份 (保留最近 7 天)
find ${BACKUP_DIR} -name "chatroom_backup_*.tar.gz" -mtime +7 -delete

echo "🧹 舊備份清理完成"
```

## 故障排除

### 常見問題與解決方案

#### 1. WebSocket 連線失敗

```bash
# 檢查 WebSocket 連線
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Key: test" \
     -H "Sec-WebSocket-Version: 13" \
     http://localhost:8000/ws/test-room?token=your_jwt_token
```

#### 2. 資料庫連線問題

```bash
# 檢查 MongoDB 狀態
docker exec chatroom-mongo mongo --eval "db.adminCommand('ismaster')"

# 檢查 Redis 狀態
docker exec chatroom-redis redis-cli ping
```

#### 3. 記憶體不足

```bash
# 檢查容器資源使用
docker stats

# 設定記憶體限制
# 在 docker-compose.yml 中添加：
# deploy:
#   resources:
#     limits:
#       memory: 512M
```

#### 4. 磁碟空間不足

```bash
# 清理無用的 Docker 資源
docker system prune -a

# 檢查磁碟使用
df -h
du -sh /var/lib/docker
```

### 日誌管理

#### 日誌收集配置

```yaml
# 在服務中添加日誌配置
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

#### 日誌查看命令

```bash
# 查看服務日誌
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mongodb

# 查看特定時間範圍日誌
docker-compose logs --since "2025-01-01T00:00:00" backend
```

這個部署指南提供了從本地開發到生產環境的完整部署策略，讓開發者可以根據需求選擇合適的部署方式。